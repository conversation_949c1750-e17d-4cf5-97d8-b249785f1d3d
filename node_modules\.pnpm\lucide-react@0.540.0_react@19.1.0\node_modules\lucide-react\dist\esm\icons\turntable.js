/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M10 12.01h.01", key: "7rp0yl" }],
  ["path", { d: "M18 8v4a8 8 0 0 1-1.07 4", key: "1st48v" }],
  ["circle", { cx: "10", cy: "12", r: "4", key: "19levz" }],
  ["rect", { x: "2", y: "4", width: "20", height: "16", rx: "2", key: "izxlao" }]
];
const Turntable = createLucideIcon("turntable", __iconNode);

export { __iconNode, Turntable as default };
//# sourceMappingURL=turntable.js.map
